import { useState } from "react";
import { She<PERSON>, SheetContent } from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { X, Trash2, Plus } from "lucide-react";
import { useOrganizationContext } from "@/features/organizations/context";
import { useAttributeTypeConfigs } from "@/hooks/useBusinessAttributes";

interface PatientSettingsSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSave?: (settings: PatientSettings) => void;
}

interface DropdownOption {
	id: string;
	value: string;
}

interface PatientField {
	id: string;
	label: string;
	type: string;
	isRequired: boolean;
	isValidator: boolean;
	options?: DropdownOption[];
}

export interface PatientSettings {
	customFields: Pat<PERSON><PERSON>ield[];
	systemFields: PatientField[];
}

export function PatientSettingsSheet({
	open,
	onOpenChange,
	onSave,
}: PatientSettingsSheetProps) {
	const { organizationId } = useOrganizationContext();
	const { data: typeConfigs, isLoading: isLoadingTypeConfigs } =
		useAttributeTypeConfigs(organizationId || 0, {
			enabled: !!organizationId,
		});

	const [settings, setSettings] = useState<PatientSettings>({
		customFields: [
			{
				id: "gender",
				label: "Gender",
				type: "select",
				isRequired: true,
				isValidator: false,
				options: [
					{ id: "1", value: "Male" },
					{ id: "2", value: "Female" },
					{ id: "3", value: "Other" },
				],
			},
		],
		systemFields: [
			{
				id: "fullName",
				label: "Full Name",
				type: "text",
				isRequired: true,
				isValidator: false,
			},
			{
				id: "phoneNumber",
				label: "Phone Number",
				type: "text",
				isRequired: true,
				isValidator: false,
			},
			{
				id: "email",
				label: "Email",
				type: "text",
				isRequired: true,
				isValidator: false,
			},
			{
				id: "dateOfBirth",
				label: "Date of Birth",
				type: "date",
				isRequired: true,
				isValidator: false,
			},
		],
	});

	const handleFieldUpdate = (
		fieldType: "customFields" | "systemFields",
		fieldId: string,
		updates: Partial<PatientField>
	) => {
		setSettings((prev) => ({
			...prev,
			[fieldType]: prev[fieldType].map((field) => {
				if (field.id === fieldId) {
					const updatedField = { ...field, ...updates };

					// If type is changing to select and field doesn't have options, add default options
					if (updates.type === "select" && !updatedField.options) {
						updatedField.options = [
							{ id: "1", value: "Option 1" },
							{ id: "2", value: "Option 2" },
						];
					}

					// If type is changing from select, remove options
					if (
						updates.type &&
						updates.type !== "select" &&
						field.type === "select"
					) {
						delete updatedField.options;
					}

					return updatedField;
				}
				return field;
			}),
		}));
	};

	const handleAddDropdownOption = (fieldId: string, value: string = "") => {
		setSettings((prev) => ({
			...prev,
			systemFields: prev.systemFields.map((field) =>
				field.id === fieldId
					? {
							...field,
							options: [
								...(field.options || []),
								{ id: Date.now().toString(), value },
							],
						}
					: field
			),
		}));
	};

	const handleRemoveDropdownOption = (fieldId: string, optionId: string) => {
		setSettings((prev) => ({
			...prev,
			systemFields: prev.systemFields.map((field) =>
				field.id === fieldId
					? {
							...field,
							options: field.options?.filter(
								(opt) => opt.id !== optionId
							),
						}
					: field
			),
		}));
	};

	const handleAddNewField = () => {
		const defaultType = typeConfigs?.data?.[0]?.key || "text";
		const newField: PatientField = {
			id: Date.now().toString(),
			label: "New Field",
			type: defaultType,
			isRequired: false,
			isValidator: false,
			...(defaultType === "select" && { options: [] }),
		};
		setSettings((prev) => ({
			...prev,
			systemFields: [...prev.systemFields, newField],
		}));
	};

	const handleSave = () => {
		onSave?.(settings);
		onOpenChange(false);
	};

	const handleClose = () => {
		onOpenChange(false);
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="w-full !max-w-[600px] p-0 [&>button]:hidden">
				<div className="flex h-full max-h-screen flex-col">
					{/* Header */}
					<div className="flex flex-shrink-0 flex-col gap-2.5 p-6 pb-0">
						<div className="flex h-14 items-start justify-start gap-2.5">
							<div className="flex flex-1 flex-col items-start justify-start gap-2">
								<div className="font-inter text-base leading-7 font-semibold text-gray-900">
									Edit Table
								</div>
								<div className="font-inter text-xs leading-4 font-normal text-gray-600">
									Select all the data points to collect from
									Patients. Set which fields are required and
									which two fields will be used as the
									validators from the list below.
								</div>
							</div>
							<div className="flex items-start justify-start gap-2.5">
								<Button
									variant="ghost"
									size="icon"
									onClick={handleClose}
									className="h-9 w-9 rounded-md"
								>
									<X className="h-4 w-4" />
								</Button>
							</div>
						</div>
					</div>
					<div className="min-h-0 flex-1 overflow-y-auto p-6 pt-6">
						<div className="flex flex-col gap-6">
							<div className="flex w-full items-center gap-6">
								<div className="font-inter text-base leading-none font-semibold text-gray-900">
									Table Titles for All Patients
								</div>
							</div>

							{settings.systemFields.map((field) => (
								<div
									key={field.id}
									className="flex flex-col gap-3"
								>
									<div className="flex items-center gap-3">
										<Input
											value={field.label}
											onChange={(e) =>
												handleFieldUpdate(
													"systemFields",
													field.id,
													{
														label: e.target.value,
													}
												)
											}
											className="h-9 flex-1 text-xs"
											placeholder="Field name"
										/>
										<Button
											variant="ghost"
											size="sm"
											className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
										>
											<Trash2 className="h-4 w-4" />
										</Button>
									</div>
									<div className="flex items-center justify-between">
										<div className="flex w-64 flex-col gap-2">
											<Select
												value={field.type}
												onValueChange={(value) =>
													handleFieldUpdate(
														"systemFields",
														field.id,
														{
															type:
																typeof value ===
																"string"
																	? value
																	: value[0],
														}
													)
												}
											>
												<SelectTrigger className="h-9 w-full text-xs">
													<SelectValue />
												</SelectTrigger>
												<SelectContent>
													{typeConfigs?.data?.map(
														(config) => (
															<SelectItem
																key={config.key}
																value={
																	config.key
																}
															>
																{config.label}
															</SelectItem>
														)
													)}
												</SelectContent>
											</Select>
										</div>
										<div className="flex w-28 items-center justify-center gap-1 py-2">
											<div className="flex items-center gap-1.5">
												<Switch
													checked={field.isRequired}
													onCheckedChange={(
														checked
													) =>
														handleFieldUpdate(
															"systemFields",
															field.id,
															{
																isRequired:
																	checked,
															}
														)
													}
												/>
												<div className="text-xs font-normal text-gray-500">
													Required
												</div>
											</div>
										</div>
										<div className="flex w-28 items-center justify-center gap-1 py-2">
											<div className="flex items-center gap-1.5">
												<Switch
													checked={field.isValidator}
													onCheckedChange={(
														checked
													) =>
														handleFieldUpdate(
															"systemFields",
															field.id,
															{
																isValidator:
																	checked,
															}
														)
													}
												/>
												<div className="text-xs font-normal text-gray-500">
													Validator
												</div>
											</div>
										</div>
									</div>
									{field.type === "select" &&
										field.options && (
											<div className="ml-4 flex flex-col gap-2">
												{field.options.map(
													(option, index) => (
														<div
															key={option.id}
															className="flex items-center gap-2"
														>
															<div className="w-20 text-xs text-gray-600">
																Dropdown Option{" "}
																{index + 1}
															</div>
															<Input
																value={
																	option.value
																}
																onChange={(
																	e
																) => {
																	const updatedOptions =
																		field.options?.map(
																			(
																				opt
																			) =>
																				opt.id ===
																				option.id
																					? {
																							...opt,
																							value: e
																								.target
																								.value,
																						}
																					: opt
																		);
																	handleFieldUpdate(
																		"systemFields",
																		field.id,
																		{
																			options:
																				updatedOptions,
																		}
																	);
																}}
																className="h-9 flex-1 text-xs"
																placeholder="Option value"
															/>
															<Button
																variant="ghost"
																size="sm"
																onClick={() =>
																	handleRemoveDropdownOption(
																		field.id,
																		option.id
																	)
																}
																className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
															>
																<Trash2 className="h-3 w-3" />
															</Button>
															<Button
																variant="ghost"
																size="sm"
																onClick={() =>
																	handleAddDropdownOption(
																		field.id,
																		`Option ${(field.options?.length || 0) + 1}`
																	)
																}
																className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
															>
																<Plus className="h-3 w-3" />
															</Button>
														</div>
													)
												)}
											</div>
										)}
								</div>
							))}
							<div className="flex h-6 items-center justify-end py-0.5">
								<div className="flex items-center gap-3">
									<button
										onClick={handleAddNewField}
										className="flex items-center justify-center overflow-hidden rounded-[1px]"
									>
										<Plus className="h-5 w-5 text-slate-900" />
									</button>
									<div className="font-inter text-xs leading-none font-normal text-slate-900">
										Add New Title
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="flex-shrink-0 bg-white p-6">
						<div className="flex items-center justify-end gap-3">
							<Button
								variant="outline"
								onClick={handleClose}
								className="h-9 min-w-32"
							>
								Cancel
							</Button>
							<Button
								onClick={handleSave}
								className="h-9 min-w-32 bg-[#005893]"
							>
								Edit
							</Button>
						</div>
					</div>
				</div>
			</SheetContent>
		</Sheet>
	);
}
